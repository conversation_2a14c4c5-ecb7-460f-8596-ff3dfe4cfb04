import {   mockReports } from '@/constants/mock-data';

// Action Types
export const DONATIONS_ACTIONS = {
  FETCH_DONATIONS_REQUEST: 'donations/FETCH_DONATIONS_REQUEST',
  FETCH_DONATIONS_SUCCESS: 'donations/FETCH_DONATIONS_SUCCESS',
  FETCH_DONATIONS_FAILURE: 'donations/FETCH_DONATIONS_FAILURE',
  
  FETCH_KAFALAT_REQUEST: 'donations/FETCH_KAFALAT_REQUEST',
  FETCH_KAFALAT_SUCCESS: 'donations/FETCH_KAFALAT_SUCCESS',
  FETCH_KAFALAT_FAILURE: 'donations/FETCH_KAFALAT_FAILURE',
  
  FETCH_REPORTS_REQUEST: 'donations/FETCH_REPORTS_REQUEST',
  FETCH_REPORTS_SUCCESS: 'donations/FETCH_REPORTS_SUCCESS',
  FETCH_REPORTS_FAILURE: 'donations/FETCH_REPORTS_FAILURE',
};

// Action Creators
export const fetchDonationsRequest = () => ({
  type: DONATIONS_ACTIONS.FETCH_DONATIONS_REQUEST,
});

export const fetchDonationsSuccess = (donations: any) => ({
  type: DONATIONS_ACTIONS.FETCH_DONATIONS_SUCCESS,
  payload: donations,
});

export const fetchDonationsFailure = (error: string) => ({
  type: DONATIONS_ACTIONS.FETCH_DONATIONS_FAILURE,
  payload: error,
});

export const fetchKafalatRequest = () => ({
  type: DONATIONS_ACTIONS.FETCH_KAFALAT_REQUEST,
});

export const fetchKafalatSuccess = (kafalat:  any) => ({
  type: DONATIONS_ACTIONS.FETCH_KAFALAT_SUCCESS,
  payload: kafalat,
});

export const fetchKafalatFailure = (error: string) => ({
  type: DONATIONS_ACTIONS.FETCH_KAFALAT_FAILURE,
  payload: error,
});

export const fetchReportsRequest = () => ({
  type: DONATIONS_ACTIONS.FETCH_REPORTS_REQUEST,
});

export const fetchReportsSuccess = (reports: typeof mockReports) => ({
  type: DONATIONS_ACTIONS.FETCH_REPORTS_SUCCESS,
  payload: reports,
});

export const fetchReportsFailure = (error: string) => ({
  type: DONATIONS_ACTIONS.FETCH_REPORTS_FAILURE,
  payload: error,
});