import React, { useEffect } from "react";
import { Tabs } from "expo-router";
import { colors } from "@/constants/colors";
import { 
  Home, 
  Heart, 
  FileText, 
  User,
  Bell
} from "lucide-react-native";
import { TouchableOpacity, View, StyleSheet, Image } from "react-native";
import { useRouter } from "expo-router";
import { useAppSelector } from "@/store/redux/hooks";

export default function TabLayout() {
  const router = useRouter();
  const { unreadCount } = useAppSelector((state: { notifications: any; }) => state.notifications);
  const { isLoading } = useAppSelector((state: { notifications: any; }) => state.notifications);

  const handleNotificationsPress = () => {
    if (!isLoading) {
      // Navigate to the notifications screen
      router.push('/notifications');
    }
  };

  useEffect(() => { 
    console.log('unreadCount', unreadCount);
  }, [unreadCount]);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.text.secondary,
        tabBarStyle: {
          borderTopWidth: 1,
          borderTopColor: colors.border,
          height: 56,
          paddingBottom: 6,
          paddingTop: 6,
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: colors.background,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        },
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 16,
        },
        headerLeft: () => (
          <View style={styles.logoContainer}>
            <Image 
              source={require('@/assets/images/logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
        ),
        headerRight: () => (
          <TouchableOpacity 
            style={styles.notificationButton}
            onPress={handleNotificationsPress}
          >
            <Bell size={20} color={colors.text.primary} />
            {unreadCount > 0 && (
              <View style={styles.badge}>
                <View style={styles.badgeInner} />
              </View>
            )}
          </TouchableOpacity>
        ),
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Home",
          tabBarIcon: ({ color }) => <Home size={20} color={color} />,
        }}
      />
      <Tabs.Screen
        name="kafalat"
        options={{
          title: "Kafalat",
          tabBarIcon: ({ color }) => <Heart size={20} color={color} />,
        }}
      />
      <Tabs.Screen
        name="donations"
        options={{
          title: "Donations",
          tabBarIcon: ({ color }) => <FileText size={20} color={color} />,
        }}
      />
      <Tabs.Screen
        name="reports"
        options={{
          title: "Reports",
          tabBarIcon: ({ color }) => <FileText size={20} color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
          tabBarIcon: ({ color }) => <User size={20} color={color} />,
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  notificationButton: {
    marginRight: 16,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeInner: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.error,
  },
  logoContainer: {
    marginLeft: 16,
  },
  logo: {
    width: 30,
    height: 30,
  },
});