import { put, takeLatest, delay } from 'redux-saga/effects';
import { 
  NOTIFICATIONS_ACTIONS, 
  fetchNotificationsSuccess, 
  fetchNotificationsFailure 
} from '../actions/notifications.actions';
import { mockNotifications } from '@/constants/mock-data';

function* fetchNotificationsSaga() {
  try { 
    yield put(fetchNotificationsSuccess(mockNotifications));
  } catch (error) {
    yield put(fetchNotificationsFailure('Failed to fetch notifications'));
  }
}

export function* watchNotifications() {
  yield takeLatest(NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_REQUEST, fetchNotificationsSaga);
}