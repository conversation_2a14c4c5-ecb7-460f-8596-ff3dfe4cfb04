
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Gantt Chart Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .content {
            padding: 30px;
        }

        .view-toggle {
            margin-bottom: 30px;
        }

        .gantt-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            overflow-x: auto;
        }

        .gantt-chart {
            min-width: 800px;
        }

        .gantt-header {
            display: grid;
            grid-template-columns: 300px repeat(16, 60px);
            gap: 1px;
            margin-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
        }

        .gantt-header-cell {
            text-align: center;
            font-weight: 600;
            color: #495057;
            padding: 8px 4px;
        }

        .gantt-row {
            display: grid;
            grid-template-columns: 300px repeat(16, 60px);
            gap: 1px;
            margin-bottom: 8px;
            align-items: center;
        }

        .task-name {
            background: #e9ecef;
            padding: 12px 15px;
            border-radius: 8px;
            font-weight: 600;
            color: #495057;
        }

        .gantt-cell {
            height: 40px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            position: relative;
        }

        .gantt-bar {
            height: 100%;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .gantt-bar:hover {
            transform: scaleY(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .task-1 { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .task-2 { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .task-3 { background: linear-gradient(135deg, #45b7d1, #2980b9); }
        .task-4 { background: linear-gradient(135deg, #f9ca24, #f0932b); }
        .task-5 { background: linear-gradient(135deg, #6c5ce7, #a29bfe); }
        .task-6 { background: linear-gradient(135deg, #fd79a8, #fdcb6e); }
        .task-7 { background: linear-gradient(135deg, #00b894, #00cec9); }
        .task-8 { background: linear-gradient(135deg, #e17055, #d63031); }
        .task-9 { background: linear-gradient(135deg, #0984e3, #74b9ff); }
        .task-10 { background: linear-gradient(135deg, #00b894, #55a3ff); }

        .table-view {
            display: none;
        }

        .project-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .project-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 15px;
            text-align: left;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .project-table td {
            padding: 18px 15px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
            vertical-align: middle;
        }

        .project-table tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .project-table tr:nth-child(even) {
            background-color: #fafbfc;
        }

        .project-table tr:nth-child(even):hover {
            background-color: #f1f3f4;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            min-width: 80px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .status-planning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 1px solid #f39c12;
        }
        .status-progress {
            background: linear-gradient(135deg, #d1ecf1, #74b9ff);
            color: #0c5460;
            border: 1px solid #0984e3;
        }
        .status-completed {
            background: linear-gradient(135deg, #d4edda, #00b894);
            color: #155724;
            border: 1px solid #00b894;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 8px;
            transition: width 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .task-11 { background: linear-gradient(135deg, #a29bfe, #6c5ce7); }
        .task-12 { background: linear-gradient(135deg, #fd79a8, #e84393); }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: transform 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #495057;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .gantt-header, .gantt-row {
                grid-template-columns: 200px repeat(16, 40px);
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Project Development Gantt Chart</h1>
            <p>Comprehensive project timeline and task management dashboard</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary view-toggle" onclick="toggleView('gantt')">Gantt View</button>
            <button class="btn btn-secondary view-toggle" onclick="toggleView('table')">Table View</button>
            <button class="btn btn-secondary" onclick="exportData()">Export Data</button>
            <button class="btn btn-secondary" onclick="printChart()">Print Chart</button>
        </div>

        <div class="content">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Total Tasks</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">16</div>
                    <div class="stat-label">Project Weeks</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">75%</div>
                    <div class="stat-label">Completion Rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">Active Tasks</div>
                </div>
            </div>

            <div id="gantt-view" class="gantt-view">
                <div class="gantt-container">
                    <div class="gantt-chart">
                        <div class="gantt-header">
                            <div class="gantt-header-cell">Task Name</div>
                            <div class="gantt-header-cell">S1</div>
                            <div class="gantt-header-cell">S2</div>
                            <div class="gantt-header-cell">S3</div>
                            <div class="gantt-header-cell">S4</div>
                            <div class="gantt-header-cell">S5</div>
                            <div class="gantt-header-cell">S6</div>
                            <div class="gantt-header-cell">S7</div>
                            <div class="gantt-header-cell">S8</div>
                            <div class="gantt-header-cell">S9</div>
                            <div class="gantt-header-cell">S10</div>
                            <div class="gantt-header-cell">S11</div>
                            <div class="gantt-header-cell">S12</div>
                            <div class="gantt-header-cell">S13</div>
                            <div class="gantt-header-cell">S14</div>
                            <div class="gantt-header-cell">S15</div>
                            <div class="gantt-header-cell">S16</div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Unit Test & Integration</div>
                            <div class="gantt-cell"><div class="gantt-bar task-1">S1</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-1">S2</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Pist Audit</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-2">S3</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-2">S4</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">EPS Management</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-3">S5</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Complementary Aid Improvement</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-4">S6</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Design Improvement</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-5">S7</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-5">S8</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Dashboard Redesign & Fixing Bugs</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-6">S8</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-6">S9</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Tagging System & User Impersonation</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-7">S10</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-7">S11</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Donor Mobile Application</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-8">S12</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-8">S13</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Assistant Mobile Application</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-9">S14</div></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Excel to Database Migration</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-10">S15</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-10">S16</div></div>
                        </div>

                        <div class="gantt-row">
                            <div class="task-name">Rapport And Soutenance Preparation</div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S3</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S4</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S5</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S6</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S7</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S8</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S9</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S10</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S11</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S12</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S13</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S14</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S15</div></div>
                            <div class="gantt-cell"><div class="gantt-bar task-11">S16</div></div>
                        </div>
 
                    </div>
                </div>
            </div>

            <div id="table-view" class="table-view">
                <table class="project-table">
                    <thead>
                        <tr>
                            <th>Task ID</th>
                            <th>Task Name</th>
                            <th>Start Week</th>
                            <th>End Week</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Progress</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>T001</td>
                            <td>Unit Test & Integration</td>
                            <td>S1</td>
                            <td>S2</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-completed">Completed</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 100%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T002</td>
                            <td>Post Audit</td>
                            <td>S3</td>
                            <td>S4</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-completed">Completed</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 100%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T003</td>
                            <td>EPS Management</td>
                            <td>S5</td>
                            <td>S5</td>
                            <td>1 week</td>
                            <td><span class="status-badge status-completed">Completed</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 100%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T004</td>
                            <td>Complementary Aid Improvement</td>
                            <td>S6</td>
                            <td>S6</td>
                            <td>1 week</td>
                            <td><span class="status-badge status-completed">Completed</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 100%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T005</td>
                            <td>Design Feedback & Improvement</td>
                            <td>S7</td>
                            <td>S8</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-progress">In Progress</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 75%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T006</td>
                            <td>Dashboard & Code Search Enhancement</td>
                            <td>S8</td>
                            <td>S9</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-progress">In Progress</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 50%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T007</td>
                            <td>Tagging System & User Impersonation</td>
                            <td>S10</td>
                            <td>S11</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-progress">In Progress</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 25%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T008</td>
                            <td>Donor Mobile Application</td>
                            <td>S12</td>
                            <td>S13</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-planning">Planning</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 0%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T009</td>
                            <td>Assistant Mobile Application</td>
                            <td>S14</td>
                            <td>S14</td>
                            <td>1 week</td>
                            <td><span class="status-badge status-planning">Planning</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 0%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T010</td>
                            <td>Excel to Database Migration</td>
                            <td>S15</td>
                            <td>S16</td>
                            <td>2 weeks</td>
                            <td><span class="status-badge status-planning">Planning</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 0%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T011</td>
                            <td>Rapport Preparation</td>
                            <td>S3</td>
                            <td>S16</td>
                            <td>14 weeks</td>
                            <td><span class="status-badge status-progress">In Progress</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 60%"></div></div></td>
                        </tr>
                        <tr>
                            <td>T012</td>
                            <td>Soutenance Preparation</td>
                            <td>S3</td>
                            <td>S16</td>
                            <td>14 weeks</td>
                            <td><span class="status-badge status-progress">In Progress</span></td>
                            <td><div class="progress-bar"><div class="progress-fill" style="width: 45%"></div></div></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function toggleView(viewType) {
            const ganttView = document.getElementById('gantt-view');
            const tableView = document.getElementById('table-view');
            const buttons = document.querySelectorAll('.view-toggle');

            if (viewType === 'gantt') {
                ganttView.style.display = 'block';
                tableView.style.display = 'none';
                buttons[0].classList.add('btn-primary');
                buttons[0].classList.remove('btn-secondary');
                buttons[1].classList.add('btn-secondary');
                buttons[1].classList.remove('btn-primary');
            } else {
                ganttView.style.display = 'none';
                tableView.style.display = 'block';
                buttons[1].classList.add('btn-primary');
                buttons[1].classList.remove('btn-secondary');
                buttons[0].classList.add('btn-secondary');
                buttons[0].classList.remove('btn-primary');
            }
        }

        function exportData() {
            const table = document.querySelector('.project-table');
            let csv = '';

            // Get headers
            const headers = table.querySelectorAll('th');
            const headerRow = Array.from(headers).map(th => th.textContent).join(',');
            csv += headerRow + '\n';

            // Get data rows
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = Array.from(cells).map(td => {
                    // Clean up cell content (remove HTML tags)
                    return td.textContent.replace(/,/g, ';').trim();
                }).join(',');
                csv += rowData + '\n';
            });

            // Download CSV
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'project-gantt-data.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function printChart() {
            window.print();
        }

        // Add smooth scrolling and enhanced interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to gantt bars
            const ganttBars = document.querySelectorAll('.gantt-bar');
            ganttBars.forEach(bar => {
                bar.addEventListener('mouseenter', function() {
                    this.style.transform = 'scaleY(1.2)';
                    this.style.zIndex = '10';
                });

                bar.addEventListener('mouseleave', function() {
                    this.style.transform = 'scaleY(1)';
                    this.style.zIndex = '1';
                });
            });

            // Add click functionality to table rows
            const tableRows = document.querySelectorAll('.project-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function() {
                    // Remove previous selection
                    tableRows.forEach(r => r.classList.remove('selected'));
                    // Add selection to current row
                    this.classList.add('selected');
                });
            });
        });
    </script>

    <style>
        .project-table tbody tr.selected {
            background-color: #e3f2fd !important;
            border-left: 4px solid #667eea;
        }

        @media print {
            .controls {
                display: none;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            body {
                background: white;
            }
        }
    </style>
</body>
</html>