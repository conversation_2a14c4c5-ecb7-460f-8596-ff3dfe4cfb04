import React from 'react';
import { View, StyleSheet, Image, Text, ViewStyle } from 'react-native';
import { colors } from '@/constants/colors';

interface AvatarProps {
  source?: string;
  size?: number;
  name?: string;
  style?: ViewStyle;
}

export const Avatar: React.FC<AvatarProps> = ({ 
  source, 
  size = 40, 
  name = '',
  style
}) => {
  const getInitials = () => {
    if (!name) return '';
    
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    }
    
    return (
      nameParts[0].charAt(0).toUpperCase() + 
      nameParts[nameParts.length - 1].charAt(0).toUpperCase()
    );
  };

  const avatarStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
  };

  const textSize = {
    fontSize: size * 0.4,
  };

  return (
    <View style={[styles.container, avatarStyle, style]}>
      {source ? (
        <Image
          source={{ uri: source }}
          style={[styles.image, avatarStyle]}
        />
      ) : (
        <Text style={[styles.initials, textSize]}>{getInitials()}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: `${colors.primary}20`,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  initials: {
    color: colors.primary,
    fontWeight: 'bold',
  },
});