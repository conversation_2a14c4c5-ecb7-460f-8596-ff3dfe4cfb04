import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  TextInput
} from 'react-native';
import { colors } from '@/constants/colors';
import { Search, X, Filter, Calendar } from 'lucide-react-native';

interface FilterBarProps {
  searchValue: string;
  onSearchChange: (text: string) => void;
  onClearSearch: () => void;
  selectedFilter?: string;
  filters: string[];
  onFilterSelect: (filter: string) => void;
  showDateFilter?: boolean;
  onDateFilterPress?: () => void;
  placeholder?: string;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  searchValue,
  onSearchChange,
  onClearSearch,
  selectedFilter = 'All',
  filters,
  onFilterSelect,
  showDateFilter = false,
  onDateFilterPress,
  placeholder = 'Search...'
}) => {
  return (
    <View style={styles.container}>
      {/* Search input */}
      <View style={styles.searchContainer}>
        <Search size={16} color={colors.text.secondary} />
        <TextInput
          style={styles.searchInput}
          value={searchValue}
          onChangeText={onSearchChange}
          placeholder={placeholder}
          placeholderTextColor={colors.text.light}
        />
        {searchValue.length > 0 && (
          <TouchableOpacity onPress={onClearSearch}>
            <X size={16} color={colors.text.secondary} />
          </TouchableOpacity>
        )}
      </View>
      
      {/* Filter options */}
      <View style={styles.filtersRow}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterChip,
                selectedFilter === filter && styles.selectedFilterChip
              ]}
              onPress={() => onFilterSelect(filter)}
            >
              <Text 
                style={[
                  styles.filterText,
                  selectedFilter === filter && styles.selectedFilterText
                ]}
              >
                {filter}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        {showDateFilter && (
          <TouchableOpacity 
            style={styles.dateFilterButton}
            onPress={onDateFilterPress}
          >
            <Calendar size={16} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: colors.text.primary,
    paddingVertical: 4,
  },
  filtersRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filtersScrollContent: {
    paddingRight: 8,
    flexGrow: 1,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: `${colors.text.primary}10`,
    marginRight: 8,
  },
  selectedFilterChip: {
    backgroundColor: `${colors.primary}20`,
  },
  filterText: {
    fontSize: 12,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  selectedFilterText: {
    color: colors.primary,
    fontWeight: '600',
  },
  dateFilterButton: {
    width: 34,
    height: 34,
    borderRadius: 17,
    backgroundColor: `${colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
  },
});