import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { mockKafalat, mockKafalatOperations, mockBeneficiaries } from '@/constants/mock-data';
import { OperationCard } from '@/components/ui/OperationCard';
import { StatusBadge } from '@/components/ui/StatutBadge';
import { ArrowLeft, User, Calendar, DollarSign } from 'lucide-react-native';

export default function KafalatOperationsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  
  const [kafalat, setKafalat] = useState<typeof mockKafalat[0] | null>(null);
  const [operations, setOperations] = useState<any[]>([]);
  const [beneficiary, setBeneficiary] = useState<typeof mockBeneficiaries[0] | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch kafalat details and operations
    const fetchData = async () => {
      setIsLoading(true);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 200)); // Reduced delay
      
      // Find the kafalat
      const kafalatItem = mockKafalat.find(k => k.id === id);
      
      if (kafalatItem) {
        setKafalat(kafalatItem);
        
        // Find the beneficiary
        const beneficiaryItem = mockBeneficiaries.find(
          b => `${b.firstName} ${b.lastName}` === kafalatItem.beneficiary
        );
        
        if (beneficiaryItem) {
          setBeneficiary(beneficiaryItem);
        }
        
        // Get operations for this kafalat
        const kafalatOperations = mockKafalatOperations[id as keyof typeof mockKafalatOperations] || [];
        setOperations(kafalatOperations);
      }
      
      setIsLoading(false);
    };
    
    fetchData();
  }, [id]);

  const handleBeneficiaryPress = () => {
    if (beneficiary) {
      router.push(`/beneficiary/${beneficiary.id}`);
    }
  };
  
  const handleGoBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.customHeader}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={handleGoBack}
          >
            <ArrowLeft size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Kafalat Operations</Text>
          <View style={styles.placeholderRight} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (!kafalat) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.customHeader}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={handleGoBack}
          >
            <ArrowLeft size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Kafalat Operations</Text>
          <View style={styles.placeholderRight} />
        </View>
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Kafalat not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Calculate statistics
  const completedOperations = operations.filter(op => op.status === 'Completed');
  const plannedOperations = operations.filter(op => op.status === 'Planned');
  const completedAmount = completedOperations.reduce((sum, op) => sum + op.amount, 0);
  const plannedAmount = plannedOperations.reduce((sum, op) => sum + op.amount, 0);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.customHeader}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleGoBack}
        >
          <ArrowLeft size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Kafalat Operations</Text>
        <View style={styles.placeholderRight} />
      </View>

      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.title}>{kafalat.service} Support</Text>
          <StatusBadge status={kafalat.status as any} />
        </View>
        
        <TouchableOpacity 
          style={styles.beneficiaryButton}
          onPress={handleBeneficiaryPress}
        >
          <User size={16} color={colors.primary} />
          <Text style={styles.beneficiaryText}>{kafalat.beneficiary}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <DollarSign size={16} color={colors.success} />
          </View>
          <View>
            <Text style={styles.statValue}>${kafalat.totalExecuted}</Text>
            <Text style={styles.statLabel}>Executed</Text>
          </View>
        </View>
        
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <DollarSign size={16} color={colors.warning} />
          </View>
          <View>
            <Text style={styles.statValue}>${kafalat.totalPlanned - kafalat.totalExecuted}</Text>
            <Text style={styles.statLabel}>Remaining</Text>
          </View>
        </View>
        
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Calendar size={16} color={colors.primary} />
          </View>
          <View>
            <Text style={styles.statValue}>{operations.length}</Text>
            <Text style={styles.statLabel}>Operations</Text>
          </View>
        </View>
      </View>

      <View style={styles.operationsHeader}>
        <Text style={styles.operationsTitle}>Operations</Text>
        <View style={styles.operationsSummary}>
          <Text style={styles.operationsSummaryText}>
            {completedOperations.length} completed | ${completedAmount}
          </Text>
          <Text style={styles.operationsSummaryText}>
            {plannedOperations.length} planned | ${plannedAmount}
          </Text>
        </View>
      </View>

      <FlatList
        data={operations}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <OperationCard {...item} />
        )}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No operations found</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.card,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  placeholderRight: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notFoundText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text.primary,
  },
  beneficiaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}10`,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  beneficiaryText: {
    fontSize: 13,
    color: colors.primary,
    fontWeight: '600',
    marginLeft: 6,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 12,
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 1,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${colors.text.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.text.primary,
  },
  statLabel: {
    fontSize: 11,
    color: colors.text.secondary,
  },
  operationsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  operationsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
  },
  operationsSummary: {
    alignItems: 'flex-end',
  },
  operationsSummaryText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
});