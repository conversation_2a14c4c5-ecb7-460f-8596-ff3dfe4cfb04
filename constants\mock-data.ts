
export const mockReports = [
  {
    id: '1',
    title: 'Annual Donation Summary',
    date: '2023-12-31',
    description: 'Summary of all donations made in 2023',
    fileSize: '2.4 MB'
  },
  {
    id: '2',
    title: 'Q3 Financial Report',
    date: '2023-10-01',
    description: 'Financial breakdown for Q3 2023',
    fileSize: '1.8 MB'
  },
  {
    id: '3',
    title: 'Impact Assessment',
    date: '2023-09-15',
    description: 'Analysis of donation impact on beneficiaries',
    fileSize: '3.2 MB'
  },
  {
    id: '4',
    title: 'Monthly Donation Receipt',
    date: '2023-08-31',
    description: 'Receipt for August 2023 donations',
    fileSize: '0.5 MB'
  }
];

export const mockKafalat = [
  {
    id: '1',
    beneficiary: '<PERSON>',
    service: 'Education',
    totalPlanned: 2400,
    totalReserved: 2400,
    totalExecuted: 1800,
    status: 'Active',
    executionDate: '2023-10-05'
  },
  {
    id: '2',
    beneficiary: '<PERSON><PERSON>',
    service: 'Healthcare',
    totalPlanned: 3600,
    totalReserved: 3600,
    totalExecuted: 2700,
    status: 'Active',
    executionDate: '2023-09-28'
  },
  {
    id: '3',
    beneficiary: '<PERSON>',
    service: 'Food',
    totalPlanned: 1200,
    totalReserved: 1200,
    totalExecuted: 1200,
    status: 'Completed',
    executionDate: '2023-08-15'
  },
  {
    id: '4',
    beneficiary: 'Layla Mohamed',
    service: 'Shelter',
    totalPlanned: 4800,
    totalReserved: 3600,
    totalExecuted: 2400,
    status: 'Active',
    executionDate: '2023-10-01'
  },
  {
    id: '5',
    beneficiary: 'Youssef Ibrahim',
    service: 'Education',
    totalPlanned: 1800,
    totalReserved: 1800,
    totalExecuted: 600,
    status: 'Active',
    executionDate: '2023-09-10'
  }
];

export const mockBeneficiaries = [
  {
    id: '1',
    firstName: 'Ahmed',
    lastName: 'Hassan',
    dateOfBirth: '1995-05-12',
    sex: 'Male',
    profession: 'Student',
    type: 'Extern',
    zone: 'Cairo, Egypt',
    email: '<EMAIL>',
    telephone: '+20 ************',
    photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=faces&q=80'
  },
  {
    id: '2',
    firstName: 'Fatima',
    lastName: 'Ali',
    dateOfBirth: '1988-09-23',
    sex: 'Female',
    profession: 'Teacher',
    type: 'Intern',
    zone: 'Alexandria, Egypt',
    email: '<EMAIL>',
    telephone: '+20 ************',
    photo: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&h=400&fit=crop&crop=faces&q=80'
  },
  {
    id: '3',
    firstName: 'Omar',
    lastName: 'Khalid',
    dateOfBirth: '1975-03-18',
    sex: 'Male',
    profession: 'Farmer',
    type: 'Extern',
    zone: 'Luxor, Egypt',
    email: '<EMAIL>',
    telephone: '+20 ************',
    photo: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=400&h=400&fit=crop&crop=faces&q=80'
  },
  {
    id: '4',
    firstName: 'Layla',
    lastName: 'Mohamed',
    dateOfBirth: '2001-11-05',
    sex: 'Female',
    profession: 'Student',
    type: 'Intern',
    zone: 'Aswan, Egypt',
    email: '<EMAIL>',
    telephone: '+20 ************',
    photo: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=faces&q=80'
  },
  {
    id: '5',
    firstName: 'Youssef',
    lastName: 'Ibrahim',
    dateOfBirth: '2010-07-30',
    sex: 'Male',
    profession: 'Student',
    type: 'Extern',
    zone: 'Giza, Egypt',
    email: '<EMAIL>',
    telephone: '+20 ************',
    photo: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=400&fit=crop&crop=faces&q=80'
  }
];

export const mockNotifications = [
  {
    id: '1',
    title: 'Donation Successful',
    description: 'Your donation of $250 has been processed successfully.',
    timestamp: '2023-10-16T09:30:00Z',
    read: false,
    type: 'success'
  },
  {
    id: '2',
    title: 'New Report Available',
    description: 'Q3 Financial Report is now available for download.',
    timestamp: '2023-10-01T14:15:00Z',
    read: true,
    type: 'info'
  },
  {
    id: '3',
    title: 'Kafalat Update',
    description: 'Ahmed Hassan has received his monthly education support.',
    timestamp: '2023-09-20T11:45:00Z',
    read: false,
    type: 'info'
  },
  {
    id: '4',
    title: 'Payment Reminder',
    description: 'Your monthly donation is scheduled for tomorrow.',
    timestamp: '2023-09-14T08:00:00Z',
    read: true,
    type: 'warning'
  },
  {
    id: '5',
    title: 'Profile Updated',
    description: 'Your profile information has been updated successfully.',
    timestamp: '2023-09-05T16:20:00Z',
    read: true,
    type: 'success'
  }
];

// Enhanced user data with two donor types
export const mockUser = {
  id: '1',
  name: 'Martin Shah',
  arabicName: 'مارتن شاه',
  email: '<EMAIL>',
  totalDonations: 2200,
  donationsSince: '2022-01-01',
  photo: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=400&h=400&fit=crop&crop=faces&q=80',
  donorType: 'physical', // 'physical' or 'moral'
  
  // Physical donor fields
  physicalDonor: {
    sex: 'Male',
    id: 'ID12345678',
    profession: 'Software Engineer',
    registrationDate: '2022-01-01',
    address: '123 Main St, New York, NY 10001',
    phone: '+****************'
  },
  
  // Moral donor fields
  moralDonor: {
    companyName: 'Tech Solutions Inc.',
    arabicCompanyName: 'تك سوليوشنز',
    id: 'CORP98765432',
    sector: 'Technology',
    registrationDate: '2020-05-15',
    address: '456 Corporate Ave, San Francisco, CA 94107',
    contacts: [
      {
        name: 'Sarah Johnson',
        arabicName: 'سارة جونسون',
        sex: 'Female',
        email: '<EMAIL>',
        phone: '+****************',
        jobTitle: 'Chief Financial Officer'
      },
      {
        name: 'Michael Chen',
        arabicName: 'مايكل تشن',
        sex: 'Male',
        email: '<EMAIL>',
        phone: '+****************',
        jobTitle: 'Corporate Social Responsibility Manager'
      }
    ]
  }
};

// New mock data for kafalat operations
export const mockKafalatOperations = {
  '1': [
    {
      id: '101',
      status: 'Completed',
      amount: 200,
      executionDate: '2023-10-05',
      plannedDate: '2023-10-01',
      description: 'Monthly education support payment'
    },
    {
      id: '102',
      status: 'Completed',
      amount: 200,
      executionDate: '2023-09-05',
      plannedDate: '2023-09-01',
      description: 'Monthly education support payment'
    },
    {
      id: '103',
      status: 'Completed',
      amount: 200,
      executionDate: '2023-08-05',
      plannedDate: '2023-08-01',
      description: 'Monthly education support payment'
    },
    {
      id: '104',
      status: 'Planned',
      amount: 200,
      executionDate: '',
      plannedDate: '2023-11-01',
      description: 'Monthly education support payment'
    },
    {
      id: '105',
      status: 'Planned',
      amount: 200,
      executionDate: '',
      plannedDate: '2023-12-01',
      description: 'Monthly education support payment'
    }
  ],
  '2': [
    {
      id: '201',
      status: 'Completed',
      amount: 300,
      executionDate: '2023-09-28',
      plannedDate: '2023-09-25',
      description: 'Monthly healthcare support payment'
    },
    {
      id: '202',
      status: 'Completed',
      amount: 300,
      executionDate: '2023-08-28',
      plannedDate: '2023-08-25',
      description: 'Monthly healthcare support payment'
    },
    {
      id: '203',
      status: 'Completed',
      amount: 300,
      executionDate: '2023-07-28',
      plannedDate: '2023-07-25',
      description: 'Monthly healthcare support payment'
    },
    {
      id: '204',
      status: 'Planned',
      amount: 300,
      executionDate: '',
      plannedDate: '2023-10-25',
      description: 'Monthly healthcare support payment'
    }
  ],
  '3': [
    {
      id: '301',
      status: 'Completed',
      amount: 100,
      executionDate: '2023-08-15',
      plannedDate: '2023-08-10',
      description: 'Monthly food support payment'
    },
    {
      id: '302',
      status: 'Completed',
      amount: 100,
      executionDate: '2023-07-15',
      plannedDate: '2023-07-10',
      description: 'Monthly food support payment'
    },
    {
      id: '303',
      status: 'Completed',
      amount: 100,
      executionDate: '2023-06-15',
      plannedDate: '2023-06-10',
      description: 'Monthly food support payment'
    }
  ],
  '4': [
    {
      id: '401',
      status: 'Completed',
      amount: 400,
      executionDate: '2023-10-01',
      plannedDate: '2023-09-30',
      description: 'Monthly shelter support payment'
    },
    {
      id: '402',
      status: 'Completed',
      amount: 400,
      executionDate: '2023-09-01',
      plannedDate: '2023-08-30',
      description: 'Monthly shelter support payment'
    },
    {
      id: '403',
      status: 'Completed',
      amount: 400,
      executionDate: '2023-08-01',
      plannedDate: '2023-07-30',
      description: 'Monthly shelter support payment'
    },
    {
      id: '404',
      status: 'Planned',
      amount: 400,
      executionDate: '',
      plannedDate: '2023-11-30',
      description: 'Monthly shelter support payment'
    }
  ],
  '5': [
    {
      id: '501',
      status: 'Completed',
      amount: 150,
      executionDate: '2023-09-10',
      plannedDate: '2023-09-05',
      description: 'Monthly education support payment'
    },
    {
      id: '502',
      status: 'Completed',
      amount: 150,
      executionDate: '2023-08-10',
      plannedDate: '2023-08-05',
      description: 'Monthly education support payment'
    },
    {
      id: '503',
      status: 'Planned',
      amount: 150,
      executionDate: '',
      plannedDate: '2023-10-05',
      description: 'Monthly education support payment'
    },
    {
      id: '504',
      status: 'Planned',
      amount: 150,
      executionDate: '',
      plannedDate: '2023-11-05',
      description: 'Monthly education support payment'
    }
  ]
};

// List of available services for filtering
export const servicesList = [
  'All',
  'Education',
  'Healthcare',
  'Food',
  'Shelter',
  'Clothing',
  'Housing'
];