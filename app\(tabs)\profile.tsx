import React, { useState } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  Text,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAppSelector, useAppDispatch } from '@/store/redux/hooks';
import { logout } from '@/store/redux/actions/auth.actions';
import { ProfileHeader } from '@/components/ui/ProfileHeader';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Avatar } from '@/components/ui/Avatar';
import { 
  User, 
  Mail, 
  Phone, 
  HelpCircle, 
  Shield, 
  LogOut,
  Calendar,
  Briefcase,
  MapPin,
  Building,
  Users
} from 'lucide-react-native';

export default function ProfileScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state: { auth: any; }) => state.auth);
  const [activeTab, setActiveTab] = useState<'physical' | 'moral'>(user?.donorType || 'physical');

  const handleLogout = () => {
    router.replace('/login');
    dispatch(logout()); 
  };

  const menuItems = [
    {
      id: '1',
      title: 'Account Settings',
      icon: <User size={18} color={colors.primary} />,
      onPress: () => {}
    },
    {
      id: '2',
      title: 'Notification Preferences',
      icon: <Mail size={18} color={colors.primary} />,
      onPress: () => {}
    },
    {
      id: '3',
      title: 'Contact Support',
      icon: <Phone size={18} color={colors.primary} />,
      onPress: () => {}
    },
    {
      id: '4',
      title: 'Help & FAQ',
      icon: <HelpCircle size={18} color={colors.primary} />,
      onPress: () => {}
    },
    {
      id: '5',
      title: 'Privacy & Security',
      icon: <Shield size={18} color={colors.primary} />,
      onPress: () => {}
    }
  ];

  // Render donor type specific information
  const renderDonorInfo = () => {
    if (activeTab === 'physical') {
      // Render physical donor information
      return (
        <Card style={styles.infoCard}>
          <Text style={styles.infoTitle}>Personal Information</Text>
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <User size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Name</Text>
              <Text style={styles.infoValue}>{user?.name}</Text>
              <Text style={styles.infoValueArabic}>{user?.arabicName}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <User size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Sex</Text>
              <Text style={styles.infoValue}>{user?.physicalDonor.sex}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <Shield size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>ID</Text>
              <Text style={styles.infoValue}>{user?.physicalDonor.id}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <Briefcase size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Profession</Text>
              <Text style={styles.infoValue}>{user?.physicalDonor.profession}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <Calendar size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Registration Date</Text>
              <Text style={styles.infoValue}>
                {new Date(user?.physicalDonor.registrationDate || '').toLocaleDateString()}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <MapPin size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Address</Text>
              <Text style={styles.infoValue}>{user?.physicalDonor.address}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <Phone size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Phone</Text>
              <Text style={styles.infoValue}>{user?.physicalDonor.phone}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoRow}>
            <View style={styles.infoIconContainer}>
              <Mail size={16} color={colors.text.secondary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{user?.email}</Text>
            </View>
          </View>
        </Card>
      );
    } else {
      // Render moral donor information
      return (
        <>
          <Card style={styles.infoCard}>
            <Text style={styles.infoTitle}>Company Information</Text>
            
            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <Building size={16} color={colors.text.secondary} />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoLabel}>Company Name</Text>
                <Text style={styles.infoValue}>{user?.moralDonor.companyName}</Text>
                <Text style={styles.infoValueArabic}>{user?.moralDonor.arabicCompanyName}</Text>
              </View>
            </View>
            
            <View style={styles.infoDivider} />
            
            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <Shield size={16} color={colors.text.secondary} />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoLabel}>ID</Text>
                <Text style={styles.infoValue}>{user?.moralDonor.id}</Text>
              </View>
            </View>
            
            <View style={styles.infoDivider} />
            
            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <Briefcase size={16} color={colors.text.secondary} />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoLabel}>Sector</Text>
                <Text style={styles.infoValue}>{user?.moralDonor.sector}</Text>
              </View>
            </View>
            
            <View style={styles.infoDivider} />
            
            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <Calendar size={16} color={colors.text.secondary} />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoLabel}>Registration Date</Text>
                <Text style={styles.infoValue}>
                  {new Date(user?.moralDonor.registrationDate || '').toLocaleDateString()}
                </Text>
              </View>
            </View>
            
            <View style={styles.infoDivider} />
            
            <View style={styles.infoRow}>
              <View style={styles.infoIconContainer}>
                <MapPin size={16} color={colors.text.secondary} />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>{user?.moralDonor.address}</Text>
              </View>
            </View>
          </Card>
          
          <Text style={styles.sectionTitle}>Contacts</Text>
          
          {user?.moralDonor.contacts.map((contact: { name: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined; arabicName: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; jobTitle: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; email: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; phone: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; sex: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }, index: React.Key | null | undefined) => (
            <Card key={index} style={styles.contactCard}>
              <View style={styles.contactHeader}>
                <Avatar size={40} name={contact.name?.toString() || ''} />
                <View style={styles.contactHeaderText}>
                  <Text style={styles.contactName}>{contact.name}</Text>
                  <Text style={styles.contactNameArabic}>{contact.arabicName}</Text>
                  <Text style={styles.contactJobTitle}>{contact.jobTitle}</Text>
                </View>
              </View>
              
              <View style={styles.contactDetails}>
                <View style={styles.contactDetailRow}>
                  <Mail size={14} color={colors.text.secondary} />
                  <Text style={styles.contactDetailText}>{contact.email}</Text>
                </View>
                
                <View style={styles.contactDetailRow}>
                  <Phone size={14} color={colors.text.secondary} />
                  <Text style={styles.contactDetailText}>{contact.phone}</Text>
                </View>
                
                <View style={styles.contactDetailRow}>
                  <User size={14} color={colors.text.secondary} />
                  <Text style={styles.contactDetailText}>{contact.sex}</Text>
                </View>
              </View>
            </Card>
          ))}
        </>
      );
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <ProfileHeader
          name={user?.name || ''}
          email={user?.email || ''}
          photo={user?.photo}
          onSettingsPress={() => {}}
        />
        
        <Card style={styles.statsCard}>
          <Text style={styles.statsTitle}>Donation Statistics</Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>${user?.totalDonations || 0}</Text>
              <Text style={styles.statLabel}>Total Donated</Text>
            </View>
            
            <View style={styles.divider} />
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {new Date(user?.donationsSince || '').getFullYear()}
              </Text>
              <Text style={styles.statLabel}>Member Since</Text>
            </View>
          </View>
        </Card>
        
        {/* Donor Type Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity 
            style={[
              styles.tab, 
              activeTab === 'physical' && styles.activeTab
            ]}
            onPress={() => setActiveTab('physical')}
          >
            <User size={16} color={activeTab === 'physical' ? colors.primary : colors.text.secondary} />
            <Text style={[
              styles.tabText,
              activeTab === 'physical' && styles.activeTabText
            ]}>
              Donateur Physique
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.tab, 
              activeTab === 'moral' && styles.activeTab
            ]}
            onPress={() => setActiveTab('moral')}
          >
            <Building size={16} color={activeTab === 'moral' ? colors.primary : colors.text.secondary} />
            <Text style={[
              styles.tabText,
              activeTab === 'moral' && styles.activeTabText
            ]}>
              Donateur Moral
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Render donor information based on active tab */}
        {renderDonorInfo()}
        
        <Text style={styles.sectionTitle}>Settings</Text>
        
        <Card style={styles.menuCard}>
          {menuItems.map((item, index) => (
            <React.Fragment key={item.id}>
              <TouchableOpacity 
                style={styles.menuItem}
                onPress={item.onPress}
              >
                <View style={styles.menuIconContainer}>
                  {item.icon}
                </View>
                <Text style={styles.menuItemText}>{item.title}</Text>
              </TouchableOpacity>
              
              {index < menuItems.length - 1 && (
                <View style={styles.menuDivider} />
              )}
            </React.Fragment>
          ))}
        </Card>
        
        <Button
          title="Log Out"
          onPress={handleLogout}
          variant="outline"
          style={styles.logoutButton}
          textStyle={styles.logoutButtonText}
          leftIcon={<LogOut size={16} color={colors.error} />}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  statsCard: {
    marginTop: 20,
  },
  statsTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 14,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  divider: {
    width: 1,
    backgroundColor: colors.border,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginTop: 24,
    marginBottom: 16,
    backgroundColor: `${colors.text.primary}10`,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 10,
  },
  activeTab: {
    backgroundColor: colors.card,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  tabText: {
    fontSize: 13,
    color: colors.text.secondary,
    marginLeft: 6,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
    marginTop: 24,
    marginBottom: 14,
  },
  infoCard: {
    padding: 16,
  },
  infoTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  infoIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: `${colors.text.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    color: colors.text.primary,
  },
  infoValueArabic: {
    fontSize: 14,
    color: colors.text.primary,
    marginTop: 2,
    textAlign: 'right',
  },
  infoDivider: {
    height: 1,
    backgroundColor: colors.border,
    marginLeft: 40,
    marginBottom: 12,
  },
  contactCard: {
    marginBottom: 12,
  },
  contactHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  contactHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  contactName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
  },
  contactNameArabic: {
    fontSize: 13,
    color: colors.text.primary,
    textAlign: 'right',
  },
  contactJobTitle: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: 2,
  },
  contactDetails: {
    backgroundColor: `${colors.text.primary}05`,
    borderRadius: 8,
    padding: 10,
  },
  contactDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  contactDetailText: {
    fontSize: 13,
    color: colors.text.secondary,
    marginLeft: 8,
  },
  menuCard: {
    padding: 0,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
  },
  menuIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  menuItemText: {
    fontSize: 14,
    color: colors.text.primary,
  },
  menuDivider: {
    height: 1,
    backgroundColor: colors.border,
    marginLeft: 60,
  },
  logoutButton: {
    marginTop: 28,
    marginBottom: 16,
    borderColor: colors.error,
  },
  logoutButtonText: {
    color: colors.error,
    marginLeft: 8,
  },
});