import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { StatusBadge } from './StatutBadge';
import { formatDate } from '@/utils/date-utils';

interface KafalatCardProps {
  id: string;
  beneficiary: string;
  service: string;
  totalPlanned: number;
  totalReserved: number;
  totalExecuted: number;
  status: string;
  executionDate?: string;
  onPress?: () => void;
}

export const KafalatCard: React.FC<KafalatCardProps> = ({
  beneficiary,
  service,
  totalPlanned,
  totalReserved,
  totalExecuted,
  status,
  executionDate,
  onPress,
}) => {
  const progress = totalExecuted / totalPlanned;

  return (
    <TouchableOpacity 
      style={styles.container}
      activeOpacity={0.7} onPress={onPress} 
    >
      <View style={styles.header}>
        <Text style={styles.beneficiary}>{beneficiary}</Text>
        <StatusBadge status={status as any} size="small" />
      </View>
      
      <View style={styles.serviceContainer}>
        <Text style={styles.serviceText}>{service}</Text>
      </View>
      
      <View style={styles.amountsContainer}>
        <View style={styles.amountItem}>
          <Text style={styles.amountValue}>${totalPlanned}</Text>
          <Text style={styles.amountLabel}>Planned</Text>
        </View>
        
        <View style={styles.amountItem}>
          <Text style={styles.amountValue}>${totalReserved}</Text>
          <Text style={styles.amountLabel}>Reserved</Text>
        </View>
        
        <View style={styles.amountItem}>
          <Text style={styles.amountValue}>${totalExecuted}</Text>
          <Text style={styles.amountLabel}>Executed</Text>
        </View>
      </View>
      
      {executionDate && (
        <View style={styles.executionDateContainer}>
          <Text style={styles.executionDateLabel}>Last Execution:</Text>
          <Text style={styles.executionDateValue}>{formatDate(executionDate)}</Text>
        </View>
      )}
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <View 
            style={[
              styles.progressFill,
              { width: `${progress * 100}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {Math.round(progress * 100)}% Complete
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 14,
    marginBottom: 14,
    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.border,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  beneficiary: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
  },
  serviceContainer: {
    alignSelf: 'flex-start',
    backgroundColor: `${colors.kafalat}20`,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 10,
    marginBottom: 14,
  },
  serviceText: {
    color: colors.kafalat,
    fontWeight: '600',
    fontSize: 11,
  },
  amountsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 14,
  },
  amountItem: {
    alignItems: 'center',
  },
  amountValue: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 2,
  },
  amountLabel: {
    fontSize: 11,
    color: colors.text.secondary,
  },
  executionDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingTop: 4,
    paddingBottom: 6,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  executionDateLabel: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  executionDateValue: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.kafalat,
  },
  progressContainer: {
    marginTop: 4,
  },
  progressBackground: {
    height: 6,
    backgroundColor: `${colors.primary}20`,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 6,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  progressText: {
    fontSize: 11,
    color: colors.text.secondary,
    textAlign: 'right',
  },
});
