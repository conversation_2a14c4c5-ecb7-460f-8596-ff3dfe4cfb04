import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  Linking
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { mockBeneficiaries } from '@/constants/mock-data';
import { Avatar } from '@/components/ui/Avatar';
import { Card } from '@/components/ui/Card';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Briefcase, 
  Calendar, 
  User,
  ArrowLeft
} from 'lucide-react-native';

export default function BeneficiaryDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  
  const beneficiary = mockBeneficiaries.find(b => b.id === id);
  
  const handleGoBack = () => {
    router.back();
  };
  
  if (!beneficiary) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.customHeader}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={handleGoBack}
          >
            <ArrowLeft size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Beneficiary Details</Text>
          <View style={styles.placeholderRight} />
        </View>
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Beneficiary not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleEmailPress = () => {
    Linking.openURL(`mailto:${beneficiary.email}`);
  };

  const handlePhonePress = () => {
    Linking.openURL(`tel:${beneficiary.telephone}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.customHeader}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleGoBack}
        >
          <ArrowLeft size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Beneficiary Details</Text>
        <View style={styles.placeholderRight} />
      </View>
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.header}>
          <Avatar 
            source={beneficiary.photo} 
            size={90} 
            name={`${beneficiary.firstName} ${beneficiary.lastName}`}
            style={styles.avatar}
          />
          
          <Text style={styles.name}>
            {beneficiary.firstName} {beneficiary.lastName}
          </Text>
          
          <View style={styles.badgeContainer}>
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{beneficiary.type}</Text>
            </View>
          </View>
        </View>
        
        <Card style={styles.contactCard}>
          <TouchableOpacity 
            style={styles.contactItem}
            onPress={handleEmailPress}
          >
            <View style={[styles.iconContainer, styles.emailIcon]}>
              <Mail size={18} color={colors.primary} />
            </View>
            <View style={styles.contactTextContainer}>
              <Text style={styles.contactLabel}>Email</Text>
              <Text style={styles.contactValue}>{beneficiary.email}</Text>
            </View>
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.contactItem}
            onPress={handlePhonePress}
          >
            <View style={[styles.iconContainer, styles.phoneIcon]}>
              <Phone size={18} color={colors.success} />
            </View>
            <View style={styles.contactTextContainer}>
              <Text style={styles.contactLabel}>Phone</Text>
              <Text style={styles.contactValue}>{beneficiary.telephone}</Text>
            </View>
          </TouchableOpacity>
        </Card>
        
        <Text style={styles.sectionTitle}>Personal Information</Text>
        
        <Card style={styles.infoCard}>
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Calendar size={16} color={colors.text.secondary} />
            </View>
            <View>
              <Text style={styles.infoLabel}>Date of Birth</Text>
              <Text style={styles.infoValue}>
                {formatDate(beneficiary.dateOfBirth)}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <User size={16} color={colors.text.secondary} />
            </View>
            <View>
              <Text style={styles.infoLabel}>Sex</Text>
              <Text style={styles.infoValue}>{beneficiary.sex}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Briefcase size={16} color={colors.text.secondary} />
            </View>
            <View>
              <Text style={styles.infoLabel}>Profession</Text>
              <Text style={styles.infoValue}>{beneficiary.profession}</Text>
            </View>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <MapPin size={16} color={colors.text.secondary} />
            </View>
            <View>
              <Text style={styles.infoLabel}>Zone</Text>
              <Text style={styles.infoValue}>{beneficiary.zone}</Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.card,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  placeholderRight: {
    width: 40,
  },
  scrollContent: {
    padding: 16,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notFoundText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    marginBottom: 14,
  },
  name: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 6,
  },
  badgeContainer: {
    flexDirection: 'row',
  },
  badge: {
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 10,
  },
  badgeText: {
    color: colors.primary,
    fontWeight: '600',
    fontSize: 12,
  },
  contactCard: {
    marginBottom: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  emailIcon: {
    backgroundColor: `${colors.primary}15`,
  },
  phoneIcon: {
    backgroundColor: `${colors.success}15`,
  },
  contactTextContainer: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 13,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  contactValue: {
    fontSize: 14,
    color: colors.text.primary,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 10,
  },
  infoCard: {
    marginBottom: 20,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  infoIconContainer: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  infoLabel: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  infoValue: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
  },
  infoDivider: {
    height: 1,
    backgroundColor: colors.border,
  },
});