import React, { useEffect, useState } from 'react';
import { 
  View, 
  StyleSheet, 
  FlatList, 
  Text,
  ActivityIndicator,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAppSelector, useAppDispatch } from '@/store/redux/hooks';
import { fetchDonationsRequest } from '@/store/redux/actions/donations.actions';
import { DonationCard } from '@/components/ui/DonationCard';
import { FilterBar } from '@/components/ui/FilterBar';
import { servicesList } from '@/constants/mock-data';
import { DateRangePicker } from '@/components/ui/DateRangePicker';
import { ArrowDownLeft, ArrowUpRight } from 'lucide-react-native';
import { router, useFocusEffect } from 'expo-router';

export default function DonationsScreen() {
  const dispatch = useAppDispatch();
  const { donations, isLoading } = useAppSelector((state: { donations: any; }) => state.donations);

  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All');
  const [filteredDonations, setFilteredDonations] = useState(donations);
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [activeDirection, setActiveDirection] = useState<'All' | 'Entrée' | 'Sortie'>('All');

  useFocusEffect(
    React.useCallback(() => {
      console.log('fetchDonationsRequest');
      dispatch(fetchDonationsRequest());
    }, [dispatch])
  );

  // Apply filters whenever donations data or filter criteria change
  useEffect(() => {
    if (!donations || !donations.length) return;

    let result = [...donations];

    // Filter by direction
    if (activeDirection !== 'All') {
      result = result.filter(item => item?.direction === activeDirection);
    }

    // Filter by search query (service name)
    if (searchQuery) {
      result = result.filter(item => 
        item?.service?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by service
    if (selectedService !== 'All') {
      result = result.filter(item => item?.service === selectedService);
    }

    // Filter by date range
    if (startDate && endDate) {
      result = result.filter(item => {
        const itemDate = new Date(item?.date || item?.executionDate || '');
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // Include the entire end date
        
        return itemDate >= start && itemDate <= end;
      });
    }

    setFilteredDonations(result);
  }, [donations, searchQuery, selectedService, startDate, endDate, activeDirection]);

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  const handleServiceSelect = (service: string) => {
    setSelectedService(service);
  };

  const handleDateFilterPress = () => {
    setIsDatePickerVisible(true);
  };

  const handleDateRangeApply = (start: string, end: string) => {
    setStartDate(start);
    setEndDate(end);
  };

  if (isLoading && (!donations || donations.length === 0)) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <View style={styles.header}>
        <Text style={styles.title}>Your Donations</Text>
        <Text style={styles.subtitle}>
          Track all your donations and their status
        </Text>
      </View>

      {/* Direction filter */}
      <View style={styles.directionFilterContainer}>
        <TouchableOpacity
          style={[
            styles.directionButton,
            activeDirection === 'All' && styles.activeDirectionButton
          ]}
          onPress={() => setActiveDirection('All')}
        >
          <Text style={[
            styles.directionButtonText,
            activeDirection === 'All' && styles.activeDirectionButtonText
          ]}>
            All
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.directionButton,
            activeDirection === 'Entrée' && styles.activeDirectionButton,
            styles.entreeButton
          ]}
          onPress={() => setActiveDirection('Entrée')}
        >
          <ArrowDownLeft size={14} color={activeDirection === 'Entrée' ? colors.success : colors.text.secondary} />
          <Text style={[
            styles.directionButtonText,
            activeDirection === 'Entrée' && styles.entreeButtonText
          ]}>
            Entrée
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.directionButton,
            activeDirection === 'Sortie' && styles.activeDirectionButton,
            styles.sortieButton
          ]}
          onPress={() => setActiveDirection('Sortie')}
        >
          <ArrowUpRight size={14} color={activeDirection === 'Sortie' ? colors.accent : colors.text.secondary} />
          <Text style={[
            styles.directionButtonText,
            activeDirection === 'Sortie' && styles.sortieButtonText
          ]}>
            Sortie
          </Text>
        </TouchableOpacity>
      </View>

      {/* Filter bar component */}
      <View style={styles.filterContainer}>
         
        
        {/* Show active filters */}
        {(selectedService !== 'All' || (startDate && endDate)) && (
          <View style={styles.activeFiltersContainer}>
            <Text style={styles.activeFiltersText}>Active filters:</Text>
            {selectedService !== 'All' && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterText}>
                  {`Service: ${selectedService}`}
                </Text>
              </View>
            )}
            {startDate && endDate && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterText}>
                  {`Date: ${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      <FlatList
        data={filteredDonations || []}
        keyExtractor={(item) => item?.id || Math.random().toString()}
        renderItem={({ item }) => (
          <DonationCard
            {...item}
            onPress={() => {}}
          />
        )}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {donations && donations.length > 0 
                ? 'No donations match your filters' 
                : 'No donations found'}
            </Text>
          </View>
        }
      />

      {/* Date Range Picker Modal */}
      <DateRangePicker
        isVisible={isDatePickerVisible}
        onClose={() => setIsDatePickerVisible(false)}
        onApply={handleDateRangeApply}
        initialStartDate={startDate}
        initialEndDate={endDate}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  directionFilterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  directionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: `${colors.text.primary}10`,
    marginRight: 8,
  },
  activeDirectionButton: {
    backgroundColor: `${colors.primary}15`,
  },
  entreeButton: {
    backgroundColor: `${colors.success}10`,
  },
  sortieButton: {
    backgroundColor: `${colors.accent}10`,
  },
  directionButtonText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginLeft: 4,
  },
  activeDirectionButtonText: {
    color: colors.primary,
    fontWeight: '600',
  },
  entreeButtonText: {
    color: colors.success,
    fontWeight: '600',
  },
  sortieButtonText: {
    color: colors.accent,
    fontWeight: '600',
  },
  filterContainer: {
    paddingHorizontal: 16,
  },
  activeFiltersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 10,
  },
  activeFiltersText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginRight: 6,
  },
  activeFilterChip: {
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  activeFilterText: {
    fontSize: 11,
    color: colors.primary,
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
});