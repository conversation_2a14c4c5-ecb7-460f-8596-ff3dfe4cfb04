import { put, takeLatest, delay } from 'redux-saga/effects';
import { AUTH_ACTIONS, loginSuccess, loginFailure } from '../actions/auth.actions';
import { mockUser } from '@/constants/mock-data';

function* loginSaga(action: any) {
  try {
    const { code, password } = action.payload;
     
     
    if (code && password) {
      yield put(loginSuccess(mockUser));
    } else {
      yield put(loginFailure('Invalid code or password'));
    }
  } catch (error) {
    yield put(loginFailure('An error occurred during login'));
  }
}

export function* watchAuth() {
  yield takeLatest(AUTH_ACTIONS.LOGIN_REQUEST, loginSaga);
}