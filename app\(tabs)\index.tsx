import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAppSelector, useAppDispatch } from '@/store/redux/hooks';
import { fetchDonationsRequest, fetchReportsRequest } from '@/store/redux/actions/donations.actions';
import { DonationSummaryCard } from '@/components/ui/DonationSummaryCard';
import { SectionHeader } from '@/components/ui/SectionHeader';
import { DonationCard } from '@/components/ui/DonationCard';
import { ReportCard } from '@/components/ui/ReportCard';

export default function HomeScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state: { auth: any; }) => state.auth);
  const { donations, reports } = useAppSelector((state: { donations: any; }) => state.donations);
  const [recentDonations, setRecentDonations] = useState<any[]>([]);

  useEffect(() => {
    dispatch(fetchDonationsRequest());
    dispatch(fetchReportsRequest());
    if(donations && donations.length > 0){
      setRecentDonations(donations.slice(0, 3));
    }
  }, [dispatch]);
  
  const recentReports = reports.slice(0, 2);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>👋 Hello!</Text>
            <Text style={styles.name}>{user?.name}</Text>
          </View>
        </View>

        <DonationSummaryCard
          totalAmount={user?.totalDonations || 0}
          donationsSince={user?.donationsSince || ''}
        />

        <SectionHeader
          title="Last Donations"
          onSeeAllPress={() => router.push('/donations')}
        />

        {recentDonations.map((donation: any) => (
          <DonationCard
            key={donation.id || `donation-${Math.random()}`}
            {...donation}
            onPress={() => {}}
          />
        ))}

        <SectionHeader
          title="Last Reports"
          onSeeAllPress={() => router.push('/reports')}
        />

        {recentReports.map((report: any) => (
          <ReportCard
            key={report.id || `report-${Math.random()}`}
            {...report}
            onPress={() => {}}
            onDownload={() => {}}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
  },
  greeting: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  name: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.text.primary,
  }
});