import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { formatDate } from '@/utils/date-utils';
import { FileText, Download } from 'lucide-react-native';

interface ReportCardProps {
  id: string;
  title: string;
  date: string;
  description: string;
  fileSize: string;
  onPress?: () => void;
  onDownload?: () => void;
}

export const ReportCard: React.FC<ReportCardProps> = ({
  title,
  date,
  description,
  fileSize,
  onPress,
  onDownload,
}) => {
  const handleDownload = (e: any) => {
    e.stopPropagation();
    if (onDownload) {
      onDownload();
    }
  };

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <FileText size={22} color={colors.reports} />
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={1}>{title}</Text>
          <Text style={styles.date}>{formatDate(date)}</Text>
        </View>
        
        <Text style={styles.description} numberOfLines={2}>
          {description}
        </Text>
        
        <View style={styles.footer}>
          <Text style={styles.fileSize}>{fileSize}</Text>
          
          <TouchableOpacity 
            style={styles.downloadButton}
            onPress={handleDownload}
          >
            <Download size={16} color={colors.reports} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 14,
    marginBottom: 14,
    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.border,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: `${colors.reports}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    marginRight: 8,
  },
  date: {
    fontSize: 11,
    color: colors.text.light,
  },
  description: {
    fontSize: 13,
    color: colors.text.secondary,
    marginBottom: 10,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  fileSize: {
    fontSize: 11,
    color: colors.text.light,
  },
  downloadButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${colors.reports}15`,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
