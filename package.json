{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/native": "^7.0.0", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "expo": "~52.0.36", "expo-blur": "~14.0.1", "expo-constants": "~17.0.7", "expo-font": "~13.0.4", "expo-haptics": "~14.0.0", "expo-image": "~2.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.1", "expo-linking": "~7.0.3", "expo-location": "~18.0.7", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-web-browser": "~14.0.1", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@react-native/debugger-frontend": "^0.78.2", "@types/react": "~18.3.12", "typescript": "~5.3.3"}, "private": true}