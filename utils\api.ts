import axios from "axios";
import { Platform } from "react-native";
import { 
  mockReports,
  mockBeneficiaries,
  mockUser,
} from "@/constants/mock-data";
 
// Get environment variables
const API_URL = process.env.API_URL || "http://172.22.4.89:8001";
const API_VERSION = process.env.API_VERSION || "v1"; 

// Create axios instance
const api = axios.create({
  baseURL: `${API_URL}`, 
  headers: {
    "Content-Type": "application/json",
  },
});

// API endpoints
export const API_ENDPOINTS = {
  DONATIONS: "/mobile/donation/160/releve-compte",
  KAFALAT: "/mobile/kafalat/kafalat-for-donateur/160",
  REPORTS: "/mobile/reports",
  BENEFICIARIES: "/mobile/beneficiaries",
  AUTH: "/mobile/auth",
};

// Cache API availability result
let apiAvailableCache: boolean | null = null;
let apiCheckPromise: Promise<boolean> | null = null;

// API functions
export const apiService = {
  // Donations
  getDonations: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.DONATIONS);
      // Ensure we always return an array, even if the response is null or undefined
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.warn("Error fetching donations, using mock data:", error);
      return []; // Return empty array instead of undefined
    }
  },

  // Kafalat
  getKafalat: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.KAFALAT);
      return response.data;
    } catch (error) {
      console.warn("Error fetching kafalat, using mock data:", error);
      return error;
    }
  },

  // Reports
  getReports: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.REPORTS);
      return mockReports;
    } catch (error) {
      console.warn("Error fetching reports, using mock data:", error);
      return mockReports;
    }
  },

  // Beneficiaries
  getBeneficiaries: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.BENEFICIARIES);
      return response.data; 
    } catch (error) {
      console.warn("Error fetching beneficiaries, using mock data:", error);
      return mockBeneficiaries;
    }
  },

  // Auth
  login: async (email: string, password: string) => {
    try {
      if (false) {
        const response = await api.post(API_ENDPOINTS.AUTH + "/login", {
          email,
          password,
        });
        return response.data;
      }
      // For demo purposes, accept any non-empty email/password
      if (email && password) {
        return { success: true, user: mockUser };
      }
      throw new Error("Invalid credentials");
    } catch (error) {
      console.error("Error during login:", error);
      throw error;
    }
  },
};

export default apiService;
