import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { formatDate } from '@/utils/date-utils';

interface DonationCardProps {
  id?: string;
  date?: string;
  amount?: number;
  type?: string;
  canal?: string;
  executionDate?: string;
  executedAmount?: number;
  service?: string;
  operation?: string;
  direction?: 'Entrée' | 'Sortie';
  onPress?: () => void;
}

export const DonationCard: React.FC<DonationCardProps> = ({
  date,
  amount = 0,
  type = '',
  canal = '', 
  service = '',
  operation = '',
  direction = 'Entrée',
  onPress,
}) => {
  // Render different card layouts based on direction (Entrée or Sortie)
  if (direction === 'Entrée') {
    return (
      <TouchableOpacity 
        style={[styles.container, styles.entreeContainer]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        {/* Direction badge - moved to top right with more space */}
        <View style={[styles.directionContainer, styles.entreeDirection]}>
          <Text style={styles.directionText}>{direction}</Text>
        </View>
        
        <View style={styles.header}>
          <Text style={styles.date}>{formatDate(date || '')}</Text>
          <Text style={[styles.amount, styles.entreeAmount]}>${amount}</Text>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Type:</Text>
            <Text style={styles.detailValue}>{type || 'N/A'}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Channel:</Text>
            <Text style={styles.detailValue}>{canal || 'N/A'}</Text>
          </View> 
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Service:</Text>
            <Text style={styles.detailValue}>{service || 'N/A'}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  } else {
    // Sortie card layout
    return (
      <TouchableOpacity 
        style={[styles.container, styles.sortieContainer]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        {/* Direction badge - moved to top right with more space */}
        <View style={[styles.directionContainer, styles.sortieDirection]}>
          <Text style={styles.directionText}>{direction}</Text>
        </View>
        
        <View style={styles.header}>
          <Text style={styles.date}>{formatDate(date || '')}</Text>
          <Text style={[styles.amount, styles.sortieAmount]}>${amount}</Text>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Service:</Text>
            <Text style={styles.detailValue}>{service || 'N/A'}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 14,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.border,
    position: 'relative',
  },
  entreeContainer: {
    borderLeftWidth: 4,
    borderLeftColor: colors.success,
  },
  sortieContainer: {
    borderLeftWidth: 4,
    borderLeftColor: colors.accent,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    // Added padding to ensure header content doesn't overlap with direction badge
    paddingRight: 60,
  },
  date: {
    fontSize: 13,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  amount: {
    fontSize: 16,
    fontWeight: '700',
  },
  entreeAmount: {
    color: colors.success,
  },
  sortieAmount: {
    color: colors.accent,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginBottom: 10,
  },
  detailsContainer: {
    marginBottom: 10,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  detailValue: {
    fontSize: 13,
    color: colors.text.primary,
    fontWeight: '500',
  },
  directionContainer: {
    position: 'absolute',
    top: 14,
    right: 14,
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 8,
    zIndex: 1, // Ensure it's above other elements
  },
  entreeDirection: {
    backgroundColor: `${colors.success}15`,
  },
  sortieDirection: {
    backgroundColor: `${colors.accent}15`,
  },
  directionText: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.text.primary,
  },
});