{"expo": {"name": "expo-app", "slug": "expo-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#ffffff"}, "package": "com.hnachid.expoapp", "jsEngine": "hermes"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "5ced5c98-1095-401b-bb12-3ff4d871401e"}}, "owner": "<PERSON><PERSON><PERSON><PERSON>"}}