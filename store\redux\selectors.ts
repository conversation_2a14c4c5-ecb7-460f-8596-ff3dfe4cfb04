import { createSelector } from 'reselect';
import { RootState } from './reducers';

// Base selectors
const getAuth = (state: RootState) => state.auth;
const getDonations = (state: RootState) => state.donations;
const getNotifications = (state: RootState) => state.notifications;

// Memoized auth selectors
export const selectUser = createSelector(
  [getAuth],
  auth => auth.user
);

export const selectIsAuthenticated = createSelector(
  [getAuth],
  auth => auth.isAuthenticated
);

// Memoized donations selectors
export const selectDonationsList = createSelector(
  [getDonations],
  donations => donations.donations
);

export const selectKafalatList = createSelector(
  [getDonations],
  donations => donations.kafalat
);

export const selectReportsList = createSelector(
  [getDonations],
  donations => donations.reports
);

// Memoized notifications selectors
export const selectNotificationsList = createSelector(
  [getNotifications],
  notifications => notifications.notifications
);

export const selectUnreadCount = createSelector(
  [getNotifications],
  notifications => notifications.unreadCount
); 