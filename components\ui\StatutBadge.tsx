import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';

type StatusType = 'Active' | 'Completed' | 'Pending' | 'Cancelled';

interface StatusBadgeProps {
  status: StatusType;
  size?: 'small' | 'medium';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  size = 'medium' 
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'Active':
        return colors.success;
      case 'Completed':
        return colors.primary;
      case 'Pending':
        return colors.warning;
      case 'Cancelled':
        return colors.error;
      default:
        return colors.text.secondary;
    }
  };

  return (
    <View style={[
      styles.badge,
      { backgroundColor: `${getStatusColor()}20` },
      size === 'small' && styles.smallBadge
    ]}>
      <View style={[
        styles.dot,
        { backgroundColor: getStatusColor() }
      ]} />
      <Text style={[
        styles.text,
        { color: getStatusColor() },
        size === 'small' && styles.smallText
      ]}>
        {status}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  smallBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6,
  },
  text: {
    fontSize: 13,
    fontWeight: '600',
  },
  smallText: {
    fontSize: 11,
  },
});