import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { formatDate } from '@/utils/date-utils';
import { StatusBadge } from './StatutBadge';

interface OperationCardProps {
  id: string;
  status: string;
  amount: number;
  executionDate: string;
  plannedDate: string;
  description?: string;
}

export const OperationCard: React.FC<OperationCardProps> = ({
  status,
  amount,
  executionDate,
  plannedDate,
  description,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <StatusBadge status={status as any} size="small" />
        <Text style={styles.amount}>${amount}</Text>
      </View>
      
      <View style={styles.divider} />
      
      <View style={styles.detailsContainer}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Planned Date:</Text>
          <Text style={styles.detailValue}>{formatDate(plannedDate)}</Text>
        </View>
        
        {executionDate && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Execution Date:</Text>
            <Text style={styles.detailValue}>{formatDate(executionDate)}</Text>
          </View>
        )}
        
        {description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>{description}</Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 14,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 1,
    borderWidth: 1,
    borderColor: colors.border,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  amount: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.kafalat,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginBottom: 10,
  },
  detailsContainer: {
    marginBottom: 4,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  detailValue: {
    fontSize: 13,
    color: colors.text.primary,
    fontWeight: '500',
  },
  descriptionContainer: {
    marginTop: 6,
    paddingTop: 6,
    borderTopWidth: 1,
    borderTopColor: `${colors.border}80`,
  },
  descriptionText: {
    fontSize: 12,
    color: colors.text.secondary,
    fontStyle: 'italic',
  },
});