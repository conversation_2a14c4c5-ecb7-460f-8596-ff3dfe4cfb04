import { mockUser } from '@/constants/mock-data';

// Action Types
export const AUTH_ACTIONS = {
  LOGIN_REQUEST: 'auth/LOGIN_REQUEST',
  LOGIN_SUCCESS: 'auth/LOGIN_SUCCESS',
  LOGIN_FAILURE: 'auth/LOGIN_FAILURE',
  LOGOUT: 'auth/LOGOUT',
};

// Action Creators
export const loginRequest = (code: string, password: string) => ({
  type: AUTH_ACTIONS.LOGIN_REQUEST,
  payload: { code, password },
});

export const loginSuccess = (user: typeof mockUser) => ({
  type: AUTH_ACTIONS.LOGIN_SUCCESS,
  payload: user,
});

export const loginFailure = (error: string) => ({
  type: AUTH_ACTIONS.LOGIN_FAILURE,
  payload: error,
});

export const logout = () => ({
  type: AUTH_ACTIONS.LOGOUT,
});