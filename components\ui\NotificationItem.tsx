import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { formatDistanceToNow } from '@/utils/date-utils';
import { colors } from '@/constants/colors';
import { 
  Bell, 
  CheckCircle, 
  AlertCircle, 
  Info 
} from 'lucide-react-native';

interface NotificationItemProps {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  read: boolean;
  type: string;
  onPress: () => void;
}

export const NotificationItem: React.FC<NotificationItemProps> = ({
  title,
  description,
  timestamp,
  read,
  type,
  onPress,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle size={22} color={colors.success} />;
      case 'warning':
        return <AlertCircle size={22} color={colors.warning} />;
      case 'info':
        return <Info size={22} color={colors.primary} />;
      default:
        return <Bell size={22} color={colors.primary} />;
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, read && styles.readContainer]} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        {getIcon()}
        {!read && <View style={styles.unreadDot} />}
      </View>
      
      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
        <Text style={styles.description} numberOfLines={2}>
          {description}
        </Text>
        <Text style={styles.timestamp}>
          {formatDistanceToNow(new Date(timestamp))}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 14,
    backgroundColor: colors.card,
    borderRadius: 16,
    marginBottom: 10,
    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
    elevation: 2,
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.border,
  },
  readContainer: {
    opacity: 0.8,
    backgroundColor: `${colors.background}80`,
  },
  iconContainer: {
    marginRight: 14,
    position: 'relative',
  },
  unreadDot: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
    borderWidth: 2,
    borderColor: colors.card,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 3,
  },
  description: {
    fontSize: 13,
    color: colors.text.secondary,
    marginBottom: 6,
    lineHeight: 18,
  },
  timestamp: {
    fontSize: 11,
    color: colors.text.light,
  },
});
