import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal,
  Platform
} from 'react-native';
import { colors } from '@/constants/colors';
import { Calendar, X } from 'lucide-react-native';
import { Button } from './Button';
import { formatDate } from '@/utils/date-utils';

interface DateRangePickerProps {
  isVisible: boolean;
  onClose: () => void;
  onApply: (startDate: string, endDate: string) => void;
  initialStartDate?: string;
  initialEndDate?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  isVisible,
  onClose,
  onApply,
  initialStartDate = '',
  initialEndDate = ''
}) => {
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  
  // For demo purposes, we'll use predefined date ranges
  const dateRanges = [
    { label: 'Last 7 days', start: -7, end: 0 },
    { label: 'Last 30 days', start: -30, end: 0 },
    { label: 'Last 90 days', start: -90, end: 0 },
    { label: 'This year', start: 'year', end: 0 },
  ];
  
  const handleDateRangeSelect = (start: number | string, end: number) => {
    const endDate = new Date();
    let startDate = new Date();
    
    if (typeof start === 'number') {
      startDate.setDate(endDate.getDate() + start);
    } else if (start === 'year') {
      startDate = new Date(endDate.getFullYear(), 0, 1);
    }
    
    setStartDate(startDate.toISOString().split('T')[0]);
    setEndDate(endDate.toISOString().split('T')[0]);
  };
  
  const handleApply = () => {
    onApply(startDate, endDate);
    onClose();
  };
  
  const handleClear = () => {
    setStartDate('');
    setEndDate('');
  };
  
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Date Range</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={20} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.dateRangesContainer}>
            {dateRanges.map((range, index) => (
              <TouchableOpacity
                key={index}
                style={styles.dateRangeButton}
                onPress={() => handleDateRangeSelect(range.start, range.end)}
              >
                <Text style={styles.dateRangeText}>{range.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={styles.selectedDatesContainer}>
            <View style={styles.dateDisplay}>
              <Text style={styles.dateLabel}>Start Date</Text>
              <View style={styles.dateValueContainer}>
                <Calendar size={16} color={colors.text.secondary} />
                <Text style={styles.dateValue}>
                  {startDate ? formatDate(startDate) : 'Select date'}
                </Text>
              </View>
            </View>
            
            <View style={styles.dateSeparator}>
              <Text style={styles.dateSeparatorText}>to</Text>
            </View>
            
            <View style={styles.dateDisplay}>
              <Text style={styles.dateLabel}>End Date</Text>
              <View style={styles.dateValueContainer}>
                <Calendar size={16} color={colors.text.secondary} />
                <Text style={styles.dateValue}>
                  {endDate ? formatDate(endDate) : 'Select date'}
                </Text>
              </View>
            </View>
          </View>
          
          <Text style={styles.note}>
            Note: For this demo, please use the predefined date ranges above.
          </Text>
          
          <View style={styles.buttonsContainer}>
            <Button
              title="Clear"
              onPress={handleClear}
              variant="outline"
              size="small"
              style={styles.clearButton}
            />
            <Button
              title="Apply"
              onPress={handleApply}
              size="small"
              style={styles.applyButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 16,
    width: '90%',
    maxWidth: 400,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  closeButton: {
    padding: 4,
  },
  dateRangesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  dateRangeButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: `${colors.primary}15`,
    marginRight: 8,
    marginBottom: 8,
  },
  dateRangeText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  selectedDatesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  dateDisplay: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  dateValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.text.primary}10`,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
  },
  dateValue: {
    fontSize: 13,
    color: colors.text.primary,
    marginLeft: 6,
  },
  dateSeparator: {
    paddingHorizontal: 10,
  },
  dateSeparatorText: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  note: {
    fontSize: 12,
    color: colors.text.light,
    fontStyle: 'italic',
    marginBottom: 16,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  clearButton: {
    marginRight: 10,
  },
  applyButton: {
    minWidth: 80,
  },
});