import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { Heart } from 'lucide-react-native';

interface DonationSummaryCardProps {
  totalAmount: number;
  donationsSince: string;
}

export const DonationSummaryCard: React.FC<DonationSummaryCardProps> = ({
  totalAmount,
  donationsSince,
}) => {
  const formattedDate = new Date(donationsSince).toLocaleDateString('en-US', {
    month: 'long',
    year: 'numeric',
  });

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Heart size={22} color={colors.primary} />
      </View>
      
      <View style={styles.contentContainer}>
        <Text style={styles.title}>Total Donations</Text>
        <Text style={styles.amount}>${totalAmount}</Text>
        <Text style={styles.subtitle}>
          Since {formattedDate}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 16,
    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: `${colors.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.text.secondary,
    marginBottom: 4,
  },
  amount: {
    fontSize: 28,
    fontWeight: '800',
    color: colors.text.primary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 11,
    color: colors.text.light,
  },
});
