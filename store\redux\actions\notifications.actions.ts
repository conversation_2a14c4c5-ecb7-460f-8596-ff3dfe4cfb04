import { mockNotifications } from '@/constants/mock-data';

// Action Types
export const NOTIFICATIONS_ACTIONS = {
  FETCH_NOTIFICATIONS_REQUEST: 'notifications/FETCH_NOTIFICATIONS_REQUEST',
  FETCH_NOTIFICATIONS_SUCCESS: 'notifications/FETCH_NOTIFICATIONS_SUCCESS',
  FETCH_NOTIFICATIONS_FAILURE: 'notifications/FETCH_NOTIFICATIONS_FAILURE',
  MARK_AS_READ: 'notifications/MARK_AS_READ',
  MARK_ALL_AS_READ: 'notifications/MARK_ALL_AS_READ',
  DELETE_NOTIFICATION: 'notifications/DELETE_NOTIFICATION',
};

// Action Creators
export const fetchNotificationsRequest = () => ({
  type: NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_REQUEST,
});

export const fetchNotificationsSuccess = (notifications: typeof mockNotifications) => ({
  type: NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_SUCCESS,
  payload: notifications,
});

export const fetchNotificationsFailure = (error: string) => ({
  type: NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_FAILURE,
  payload: error,
});

export const markAsRead = (id: string) => ({
  type: NOTIFICATIONS_ACTIONS.MARK_AS_READ,
  payload: id,
});

export const markAllAsRead = () => ({
  type: NOTIFICATIONS_ACTIONS.MARK_ALL_AS_READ,
});

export const deleteNotification = (id: string) => ({
  type: NOTIFICATIONS_ACTIONS.DELETE_NOTIFICATION,
  payload: id,
});