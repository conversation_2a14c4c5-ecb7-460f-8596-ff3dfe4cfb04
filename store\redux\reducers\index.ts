import { combineReducers } from 'redux';
import { authReducer } from './auth.reducer';
import { donationsReducer } from './donations.reducer';
import { notificationsReducer } from './notifications.reducer';

export const rootReducer = combineReducers({
  auth: authReducer,
  donations: donationsReducer,
  notifications: notificationsReducer,
});

export type RootState = ReturnType<typeof rootReducer>;