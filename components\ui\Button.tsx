import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
  View
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors } from '@/constants/colors';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  gradientColors?: string[];
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  isLoading = false,
  disabled = false,
  style,
  textStyle,
  gradientColors,
  leftIcon,
  rightIcon,
  ...rest
}) => {
  const buttonStyles: ViewStyle[] = [
    styles.button,
    styles[`${size}Button`], 
    style as ViewStyle
  ];

  const textStyles: TextStyle[] = [
    styles.text,
    styles[`${size}Text`], 
    textStyle as TextStyle
  ];

  if (variant === 'outline') {
    buttonStyles.push(styles.outlineButton);
    textStyles.push(styles.outlineText);
  } else if (variant === 'text') {
    buttonStyles.push(styles.textButton);
    textStyles.push(styles.textButtonText);
  } else if (variant === 'secondary') {
    buttonStyles.push(styles.secondaryButton);
    textStyles.push(styles.secondaryText);
  }

  const handlePress = () => {
    if (!isLoading && !disabled) {
      onPress();
    }
  };

  if (variant === 'primary' || variant === 'secondary') {
    const defaultGradient = variant === 'primary' 
      ? colors.gradient.blue 
      : colors.gradient.green;
    
    const buttonGradient : any = gradientColors || defaultGradient;

    return (
      <TouchableOpacity
        onPress={handlePress}
        style={buttonStyles}
        activeOpacity={0.8}
        disabled={isLoading || disabled}
        {...rest}
      >
        <LinearGradient
          colors={buttonGradient}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          {isLoading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <View style={styles.contentContainer}>
              {leftIcon && <View style={styles.iconLeft}>{leftIcon}</View>}
              <Text style={textStyles}>{title}</Text>
              {rightIcon && <View style={styles.iconRight}>{rightIcon}</View>}
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={buttonStyles}
      activeOpacity={0.8}
      disabled={isLoading || disabled}
      {...rest}
    >
      {isLoading ? (
        <ActivityIndicator 
          color={variant === 'outline' ? colors.primary : colors.text.secondary} 
          size="small" 
        />
      ) : (
        <View style={styles.contentContainer}>
          {leftIcon && <View style={styles.iconLeft}>{leftIcon}</View>}
          <Text style={textStyles}>{title}</Text>
          {rightIcon && <View style={styles.iconRight}>{rightIcon}</View>}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: { 
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    borderRadius: 30,
  },
  gradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
  text: {
    fontWeight: '600',
    color: colors.text.white,
  },
  smallButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    height: 36,
  },
  mediumButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    height: 48,
  },
  largeButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    height: 56,
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  outlineText: {
    color: colors.primary,
  },
  textButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 8,
  },
  textButtonText: {
    color: colors.primary,
  },
  secondaryButton: {
    backgroundColor: colors.secondary,
  },
  secondaryText: {
    color: colors.text.white,
  },
  disabledButton: {
    opacity: 0.6,
  },
  disabledText: {
    opacity: 0.8,
  },
});