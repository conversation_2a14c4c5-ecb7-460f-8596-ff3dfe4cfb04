import { put, takeLatest, delay } from 'redux-saga/effects';
import { 
  DONATIONS_ACTIONS, 
  fetchDonationsSuccess, 
  fetchDonationsFailure,
  fetchKafalatSuccess,
  fetchKafalatFailure,
  fetchReportsSuccess,
  fetchReportsFailure
} from '../actions/donations.actions';
import apiService from '@/utils/api';
import {  mockReports } from '@/constants/mock-data';

function* fetchDonationsSaga(): Generator<any, void, any> {
  try { 
    const donations = yield apiService.getDonations(); 
    // Ensure donations is always an array, even if the API returns null or undefined
    yield put(fetchDonationsSuccess(donations || []));
  } catch (error) {
    // Return empty array instead of null when there's an error
    yield put(fetchDonationsSuccess([]));
    yield put(fetchDonationsFailure('Failed to fetch donations'));
  }
}

function* fetchKafalatSaga(): Generator<any, void, any> {
  try { 
    const kafalat = yield apiService.getKafalat();
    yield put(fetchKafalatSuccess(kafalat || []));
  } catch (error) {
    yield put(fetchKafalatSuccess([]));
    yield put(fetchKafalatFailure('Failed to fetch kafalat'));
  }
}

function* fetchReportsSaga(): Generator<any, void, any> {
  try { 
    // const reports = yield apiService.getReports();
    yield put(fetchReportsSuccess(mockReports || []));
  } catch (error) {
    yield put(fetchReportsSuccess([]));
    yield put(fetchReportsFailure('Failed to fetch reports'));
  }
}

export function* watchDonations() {
  yield takeLatest(DONATIONS_ACTIONS.FETCH_DONATIONS_REQUEST, fetchDonationsSaga);
  yield takeLatest(DONATIONS_ACTIONS.FETCH_KAFALAT_REQUEST, fetchKafalatSaga);
  yield takeLatest(DONATIONS_ACTIONS.FETCH_REPORTS_REQUEST, fetchReportsSaga);
}