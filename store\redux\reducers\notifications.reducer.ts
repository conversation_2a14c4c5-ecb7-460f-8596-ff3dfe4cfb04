import { mockNotifications } from '@/constants/mock-data';
import { NOTIFICATIONS_ACTIONS } from '../actions/notifications.actions';

interface NotificationsState {
  notifications: typeof mockNotifications;
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
}

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
};

export const notificationsReducer = (state = initialState, action: any): NotificationsState => {
  switch (action.type) {
    case NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_REQUEST:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_SUCCESS:
      return {
        ...state,
        notifications: action.payload,
        unreadCount: action.payload.filter((n: any) => !n.read).length,
        isLoading: false,
      };
    case NOTIFICATIONS_ACTIONS.FETCH_NOTIFICATIONS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case NOTIFICATIONS_ACTIONS.MARK_AS_READ:
      const updatedNotifications = state.notifications.map(notification => 
        notification.id === action.payload ? { ...notification, read: true } : notification
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.read).length,
      };
    case NOTIFICATIONS_ACTIONS.MARK_ALL_AS_READ:
      const allReadNotifications = state.notifications.map(notification => ({
        ...notification,
        read: true
      }));
      return {
        ...state,
        notifications: allReadNotifications,
        unreadCount: 0,
      };
    case NOTIFICATIONS_ACTIONS.DELETE_NOTIFICATION:
      const filteredNotifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(n => !n.read).length,
      };
    default:
      return state;
  }
};