import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { Avatar } from './Avatar';
import { Settings } from 'lucide-react-native';

interface ProfileHeaderProps {
  name: string;
  email: string;
  photo?: string;
  onSettingsPress?: () => void;
}

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  name,
  email,
  photo,
  onSettingsPress,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Avatar source={photo} size={60} name={name} />
        
        <View style={styles.textContainer}>
          <Text style={styles.name}>{name}</Text>
          <Text style={styles.email}>{email}</Text>
        </View>
      </View>
      
      {onSettingsPress && (
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={onSettingsPress}
        >
          <Settings size={20} color={colors.text.primary} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 14,
  },
  name: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 2,
  },
  email: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  settingsButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${colors.text.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
  },
});