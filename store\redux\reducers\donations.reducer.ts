import {   mockReports } from '@/constants/mock-data';
import { DONATIONS_ACTIONS } from '../actions/donations.actions';

interface DonationsState {
  donations: any;
  kafalat: any;
  reports: typeof mockReports;
  isLoading: boolean;
  error: string | null;
}

const initialState: DonationsState = {
  donations: [],
  kafalat: [],
  reports: [],
  isLoading: false,
  error: null,
};

export const donationsReducer = (state = initialState, action: any): DonationsState => {
  switch (action.type) {
    case DONATIONS_ACTIONS.FETCH_DONATIONS_REQUEST: 
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case DONATIONS_ACTIONS.FETCH_DONATIONS_SUCCESS:
      return {
        ...state,
        donations: action.payload,
        isLoading: false,
      };
    case DONATIONS_ACTIONS.FETCH_DONATIONS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case DONATIONS_ACTIONS.FETCH_KAFALAT_REQUEST:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case DONATIONS_ACTIONS.FETCH_KAFALAT_SUCCESS:
      return {
        ...state,
        kafalat: action.payload,
        isLoading: false,
      };
    case DONATIONS_ACTIONS.FETCH_KAFALAT_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case DONATIONS_ACTIONS.FETCH_REPORTS_REQUEST:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case DONATIONS_ACTIONS.FETCH_REPORTS_SUCCESS:
      return {
        ...state,
        reports: action.payload,
        isLoading: false,
      };
    case DONATIONS_ACTIONS.FETCH_REPORTS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    default:
      return state;
  }
};