import React, { useEffect, useState } from 'react';
import { 
  View, 
  StyleSheet, 
  FlatList, 
  Text,
  ActivityIndicator
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect, useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAppSelector, useAppDispatch } from '@/store/redux/hooks';
import { fetchKafalatRequest } from '@/store/redux/actions/donations.actions';
import { KafalatCard } from '@/components/ui/KafalatCard';
import { FilterBar } from '@/components/ui/FilterBar';
import { servicesList } from '@/constants/mock-data';
import { DateRangePicker } from '@/components/ui/DateRangePicker';

export default function KafalatScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { kafalat, isLoading } = useAppSelector((state: { donations: any; }) => state.donations);

  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All');
  const [filteredKafalat, setFilteredKafalat] = useState(kafalat);
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
 
  // Apply filters whenever kafalat data or filter criteria change
  useEffect(() => {
    if (!kafalat.length) return;

    let result = [...kafalat];

    // Filter by search query (beneficiary name)
    if (searchQuery) {
      result = result.filter(item => 
        item.beneficiary.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by service
    if (selectedService !== 'All') {
      result = result.filter(item => item.service === selectedService);
    }

    // Filter by date range
    if (startDate && endDate) {
      result = result.filter(item => {
        const itemDate = new Date(item.executionDate);
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // Include the entire end date
        
        return itemDate >= start && itemDate <= end;
      });
    }

    setFilteredKafalat(result);
  }, [kafalat, searchQuery, selectedService, startDate, endDate]);

  useFocusEffect(
    React.useCallback(() => {
      dispatch(fetchKafalatRequest());
      console.log('kafalat', kafalat);
      return () => { 
      };
    }, [dispatch])
  );
  
  const handleKafalatPress = (id: string) => {
    // Navigate to the kafalat operations page
    router.push(`/kafalat/${id}`);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  const handleServiceSelect = (service: string) => {
    setSelectedService(service);
  };

  const handleDateFilterPress = () => {
    setIsDatePickerVisible(true);
  };

  const handleDateRangeApply = (start: string, end: string) => {
    setStartDate(start);
    setEndDate(end);
  };

  if (isLoading && kafalat.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <View style={styles.header}>
        <Text style={styles.title}>Kafalat Donations</Text>
        <Text style={styles.subtitle}>
          Support our beneficiaries through ongoing donations
        </Text>
      </View>

      {/* Filter bar component */}
      <View style={styles.filterContainer}>
        <FilterBar
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          onClearSearch={handleClearSearch}
          selectedFilter={selectedService}
          filters={servicesList}
          onFilterSelect={handleServiceSelect}
          showDateFilter={true}
          onDateFilterPress={handleDateFilterPress}
          placeholder="Search by beneficiary name..."
        />
        
        {/* Show active filters */}
        {(selectedService !== 'All' || (startDate && endDate)) && (
          <View style={styles.activeFiltersContainer}>
            <Text style={styles.activeFiltersText}>Active filters:</Text>
            {selectedService !== 'All' && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterText}>
                  Service: {selectedService}
                </Text>
              </View>
            )}
            {startDate && endDate && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterText}>
                  Date: {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      <FlatList
        data={filteredKafalat}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          // Transform the data to match KafalatCard props
          const transformedItem = {
            id: item.id.toString(),
            beneficiary: item.takenInCharge?.takenInChargeBeneficiaries[0]?.beneficiary?.person?.firstName + ' ' + item.takenInCharge?.takenInChargeBeneficiaries[0]?.beneficiary?.person?.lastName || 'Unknown Beneficiary',
            service: item.takenInCharge?.services?.name || 'General Service',
            totalPlanned:    item.takenInChargeOperations?.reduce((sum: number, op: { status: string; amount: number }) => 
              sum + (  op.amount), 0) || 0,
            totalReserved: item.takenInChargeOperations?.reduce((sum: number, op: { status: string; amount: number }) => 
              sum + (op.status === 'Réservé' ? op.amount : 0), 0) || 0,
            totalExecuted: item.takenInChargeOperations?.reduce((sum: number, op: { status: string; amount: number }) => 
              sum + (op.status === 'Exécuté' || op.status === 'Clôturé' ? op.amount : 0), 0) || 0,
            status: item.takenInCharge?.status || 'PENDING',
            executionDate: item.takenInChargeOperations?.length > 0 
              ? item.takenInChargeOperations[item.takenInChargeOperations.length - 1].executionDate
              : undefined
          };
          
          return (
            <KafalatCard
              {...transformedItem}
              onPress={() => handleKafalatPress(transformedItem.id)}
            />
          );
        }}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {kafalat.length > 0 
                ? 'No kafalat donations match your filters' 
                : 'No kafalat donations found'}
            </Text>
          </View>
        }
      />

      {/* Date Range Picker Modal */}
      <DateRangePicker
        isVisible={isDatePickerVisible}
        onClose={() => setIsDatePickerVisible(false)}
        onApply={handleDateRangeApply}
        initialStartDate={startDate}
        initialEndDate={endDate}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  filterContainer: {
    paddingHorizontal: 16,
  },
  activeFiltersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 10,
  },
  activeFiltersText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginRight: 6,
  },
  activeFilterChip: {
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  activeFilterText: {
    fontSize: 11,
    color: colors.primary,
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
});